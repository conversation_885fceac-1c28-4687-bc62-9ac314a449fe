# Marker Desktop

A cross-platform desktop application built with Electron and Python, designed to run on Windows, macOS, and Linux.

## Features

- 🖥️ Cross-platform desktop app (Windows, macOS, Linux)
- 🐍 Python backend process for heavy computation
- ⚡ Real-time communication between frontend and backend
- 📦 Easy packaging and distribution
- 🎨 Modern, responsive UI

## Project Structure

```
marker-desktop/
├── src/                    # Electron frontend
│   ├── main.js            # Main Electron process
│   ├── preload.js         # Preload script for security
│   ├── index.html         # Main UI
│   ├── styles.css         # Styling
│   └── renderer.js        # Frontend JavaScript
├── python/                # Python backend
│   └── main.py           # Main Python script
├── assets/               # App icons and resources
├── package.json          # Node.js dependencies and scripts
└── README.md            # This file
```

## Prerequisites

- Node.js (v16 or higher)
- Python 3.7 or higher
- npm or yarn

## Installation

1. Clone or navigate to the project directory
2. Install Node.js dependencies:
   ```bash
   npm install
   ```

## Development

### Running in Development Mode

```bash
npm run dev
```

This will start the Electron app with developer tools enabled.

### Running in Production Mode

```bash
npm start
```

## Building for Distribution

### Build for Current Platform
```bash
npm run build
```

### Build for Specific Platforms
```bash
# Windows
npm run build:win

# macOS
npm run build:mac

# Linux
npm run build:linux
```

Built applications will be available in the `dist/` directory.

## How It Works

1. **Electron Frontend**: Provides the desktop application shell and user interface
2. **Python Backend**: Runs as a child process, handling data processing and business logic
3. **IPC Communication**: Secure communication between frontend and backend via JSON messages
4. **Cross-Platform**: Uses Electron's cross-platform capabilities for consistent experience

## Customization

### Adding Python Dependencies

Create a `requirements.txt` file in the `python/` directory:
```txt
numpy==1.21.0
requests==2.26.0
```

### Modifying the UI

- Edit `src/index.html` for structure
- Edit `src/styles.css` for styling
- Edit `src/renderer.js` for frontend logic

### Extending Python Backend

Modify `python/main.py` to add your custom Python logic. The backend communicates via JSON messages through stdin/stdout.

## Security

This app uses Electron's security best practices:
- Context isolation enabled
- Node integration disabled in renderer
- Preload script for secure IPC

## License

MIT License - feel free to use this as a starting point for your own projects!

## Next Steps

1. Add your custom Python logic to `python/main.py`
2. Customize the UI in the `src/` directory
3. Add app icons to the `assets/` directory
4. Configure build settings in `package.json`
5. Test on your target platforms
6. Build and distribute your app!
