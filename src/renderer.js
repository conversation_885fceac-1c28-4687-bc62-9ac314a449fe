// DOM elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const pythonOutput = document.getElementById('pythonOutput');
const rawPythonOutput = document.getElementById('rawPythonOutput');
const dropzone = document.getElementById('dropzone');
const fileInput = document.getElementById('fileInput');
const browseButton = document.getElementById('browseButton');
const fileInfoCard = document.getElementById('fileInfoCard');
const currentFileName = document.getElementById('currentFileName');
const currentFileSize = document.getElementById('currentFileSize');
const clearFileButton = document.getElementById('clearFile');
const resultsCard = document.getElementById('resultsCard');

// File handling state
let currentFile = null;

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    console.log('Renderer process loaded');

    // Clear placeholder text
    pythonOutput.innerHTML = '';
    rawPythonOutput.innerHTML = '';

    // Set up event listeners
    setupFileHandling();
    setupCommunication();

    // Listen for Python output
    window.electronAPI.onPythonData((event, data) => {
        displayRawPythonOutput(data);
        handlePythonMessage(data);

        // Check for Python startup errors
        if (data.includes('ERROR:') || data.includes('Failed to start Python') || data.includes('No Python installation found')) {
            showPythonError(data);
        }
    });

    // Send initial message to Python to test connection
    setTimeout(() => {
        sendToPython({ type: 'init', message: 'Frontend connected' });
    }, 2000); // Increased timeout to allow Python to start
});

function setupFileHandling() {
    // File input change
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelection(e.target.files[0]);
        }
    });

    // Browse button
    browseButton.addEventListener('click', () => {
        fileInput.click();
    });

    // Clear file button
    clearFileButton.addEventListener('click', () => {
        clearFile();
    });

    // Drag and drop events
    dropzone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropzone.classList.add('dragover');
    });

    dropzone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropzone.classList.remove('dragover');
    });

    dropzone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropzone.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        }
    });
}

function setupCommunication() {
    sendButton.addEventListener('click', sendMessageToPython);
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendMessageToPython();
        }
    });
}

async function sendMessageToPython() {
    const message = messageInput.value.trim();
    if (!message) return;
    
    try {
        const response = await sendToPython({ 
            type: 'user_message', 
            message: message,
            timestamp: new Date().toISOString()
        });
        
        if (response.success) {
            // Clear input
            messageInput.value = '';
            
            // Display sent message
            displayMessage(`→ Sent: ${message}`, 'sent');
        } else {
            displayMessage(`✗ Error: ${response.error}`, 'error');
        }
    } catch (error) {
        displayMessage(`✗ Error: ${error.message}`, 'error');
    }
}

async function sendToPython(data) {
    try {
        return await window.electronAPI.sendToPython(data);
    } catch (error) {
        console.error('Error sending to Python:', error);
        throw error;
    }
}

async function handleFileSelection(file) {
    currentFile = file;

    // Show temporary file info while processing
    showFileInfo(file.name, 'Processing...');

    try {
        // Send file to main process for temporary storage
        const result = await window.electronAPI.saveFile({
            name: file.name,
            size: file.size,
            type: file.type,
            buffer: await file.arrayBuffer()
        });

        if (result.success) {
            // Send file info to Python backend for size calculation
            sendToPython({
                type: 'file_uploaded',
                file: {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    tempPath: result.tempPath
                }
            });

        } else {
            showFileInfo(file.name, 'Error uploading file');
            console.error('File upload error:', result.error);
        }
    } catch (error) {
        showFileInfo(file.name, 'Error uploading file');
        console.error('File handling error:', error);
    }
}

function showFileInfo(name, size) {
    currentFileName.textContent = name;
    currentFileSize.textContent = size;
    fileInfoCard.style.display = 'block';
}

function showPythonError(errorMessage) {
    // Show error in the dropzone area
    const dropzoneContent = document.querySelector('.dropzone-content');
    if (dropzoneContent) {
        dropzoneContent.innerHTML = `
            <div class="dropzone-icon">⚠️</div>
            <h2 style="color: #e53e3e;">Python Backend Error</h2>
            <p style="color: #c53030; margin-bottom: 15px;">Unable to start Python backend</p>
            <div style="background: #fed7d7; padding: 15px; border-radius: 8px; margin: 15px 0; text-align: left;">
                <strong>Error Details:</strong><br>
                <code style="font-size: 0.8rem; word-break: break-word;">${errorMessage}</code>
            </div>
            <div style="background: #e6fffa; padding: 15px; border-radius: 8px; text-align: left;">
                <strong>To fix this issue:</strong><br>
                1. Install Python 3.x from <a href="https://python.org" target="_blank">python.org</a><br>
                2. Make sure Python is added to your system PATH<br>
                3. Restart the application<br>
                <br>
                <strong>Test Python installation:</strong><br>
                Open terminal/command prompt and run: <code>python --version</code> or <code>python3 --version</code>
            </div>
        `;
    }
}

function clearFile() {
    currentFile = null;

    // Reset UI
    fileInfoCard.style.display = 'none';
    resultsCard.style.display = 'none';

    // Clear file input
    fileInput.value = '';

    // Reset file info
    currentFileName.textContent = 'No file selected';
    currentFileSize.textContent = '0 bytes';

    // Clear results
    pythonOutput.innerHTML = '<p class="placeholder">File processing results will appear here...</p>';
}



function handlePythonMessage(data) {
    try {
        const parsed = JSON.parse(data);

        if (parsed.type === 'file_info') {
            // Handle file information from Python backend
            const fileData = parsed.data;
            showFileInfo(fileData.name, fileData.size_formatted);

            // Show success message in results
            resultsCard.style.display = 'block';
            displayFileResult(`✅ File processed: ${fileData.name} (${fileData.size_formatted})`);

        } else if (parsed.type === 'file_error') {
            // Handle file processing error
            const errorData = parsed.data;
            showFileInfo('Error', errorData.error);
            displayFileError(`❌ Error: ${errorData.error}`);

        } else if (parsed.type === 'file_processing_result') {
            displayFileResult(parsed.message);
        } else if (parsed.type === 'file_processing_error') {
            displayFileError(parsed.message);
        }
    } catch (e) {
        // Not JSON or not a structured message - ignore
    }
}

function displayFileResult(message) {
    displayMessage(`✅ ${message}`, 'success', pythonOutput);
}

function displayFileError(message) {
    displayMessage(`❌ ${message}`, 'error', pythonOutput);
}

function displayRawPythonOutput(data) {
    try {
        // Try to parse as JSON first
        const parsed = JSON.parse(data);
        displayMessage(`← Python: ${parsed.message || JSON.stringify(parsed)}`, 'received', rawPythonOutput);
    } catch (e) {
        // If not JSON, display as plain text
        displayMessage(`← Python: ${data.trim()}`, 'received', rawPythonOutput);
    }
}

function displayMessage(message, type, container = rawPythonOutput) {
    const messageElement = document.createElement('div');
    messageElement.className = `python-message ${type}`;
    messageElement.textContent = message;

    container.appendChild(messageElement);
    container.scrollTop = container.scrollHeight;

    // Remove placeholder if it exists
    const placeholder = container.querySelector('.placeholder');
    if (placeholder) {
        placeholder.remove();
    }
}

// Utility function to format timestamps
function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString();
}
