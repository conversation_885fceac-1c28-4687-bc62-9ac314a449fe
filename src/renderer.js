// DOM elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const pythonOutput = document.getElementById('pythonOutput');
const rawPythonOutput = document.getElementById('rawPythonOutput');
const dropzone = document.getElementById('dropzone');
const fileInput = document.getElementById('fileInput');
const browseButton = document.getElementById('browseButton');
const fileInfo = document.getElementById('fileInfo');
const removeFileButton = document.getElementById('removeFile');
const resultsCard = document.getElementById('resultsCard');

// File handling state
let currentFile = null;

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    console.log('Renderer process loaded');

    // Clear placeholder text
    pythonOutput.innerHTML = '';
    rawPythonOutput.innerHTML = '';

    // Set up event listeners
    setupFileHandling();
    setupCommunication();

    // Listen for Python output
    window.electronAPI.onPythonData((event, data) => {
        displayRawPythonOutput(data);
        handlePythonMessage(data);
    });

    // Send initial message to Python to test connection
    setTimeout(() => {
        sendToPython({ type: 'init', message: 'Frontend connected' });
    }, 1000);
});

function setupFileHandling() {
    // File input change
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelection(e.target.files[0]);
        }
    });

    // Browse button
    browseButton.addEventListener('click', () => {
        fileInput.click();
    });

    // Remove file button
    removeFileButton.addEventListener('click', () => {
        clearFile();
    });

    // Drag and drop events
    dropzone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropzone.classList.add('dragover');
    });

    dropzone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropzone.classList.remove('dragover');
    });

    dropzone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropzone.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        }
    });
}

function setupCommunication() {
    sendButton.addEventListener('click', sendMessageToPython);
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendMessageToPython();
        }
    });
}

async function sendMessageToPython() {
    const message = messageInput.value.trim();
    if (!message) return;
    
    try {
        const response = await sendToPython({ 
            type: 'user_message', 
            message: message,
            timestamp: new Date().toISOString()
        });
        
        if (response.success) {
            // Clear input
            messageInput.value = '';
            
            // Display sent message
            displayMessage(`→ Sent: ${message}`, 'sent');
        } else {
            displayMessage(`✗ Error: ${response.error}`, 'error');
        }
    } catch (error) {
        displayMessage(`✗ Error: ${error.message}`, 'error');
    }
}

async function sendToPython(data) {
    try {
        return await window.electronAPI.sendToPython(data);
    } catch (error) {
        console.error('Error sending to Python:', error);
        throw error;
    }
}

async function handleFileSelection(file) {
    currentFile = file;

    // Show file info
    document.querySelector('.dropzone-content').style.display = 'none';
    fileInfo.style.display = 'block';

    // Update file details
    document.querySelector('.file-name').textContent = file.name;
    document.querySelector('.file-size').textContent = formatFileSize(file.size);

    // Update status
    updateStatus('Uploading file...', 0);

    try {
        // Send file to main process for temporary storage
        const result = await window.electronAPI.saveFile({
            name: file.name,
            size: file.size,
            type: file.type,
            buffer: await file.arrayBuffer()
        });

        if (result.success) {
            updateStatus('File ready for processing', 100);

            // Send file info to Python backend
            sendToPython({
                type: 'file_uploaded',
                file: {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    tempPath: result.tempPath
                }
            });

            // Show results card
            resultsCard.style.display = 'block';

        } else {
            updateStatus('Error uploading file', 0);
            console.error('File upload error:', result.error);
        }
    } catch (error) {
        updateStatus('Error uploading file', 0);
        console.error('File handling error:', error);
    }
}

function clearFile() {
    currentFile = null;

    // Reset UI
    document.querySelector('.dropzone-content').style.display = 'block';
    fileInfo.style.display = 'none';
    resultsCard.style.display = 'none';

    // Clear file input
    fileInput.value = '';

    // Clear results
    pythonOutput.innerHTML = '<p class="placeholder">File processing results will appear here...</p>';
}

function updateStatus(text, progress) {
    document.getElementById('statusText').textContent = text;
    document.querySelector('.progress-fill').style.width = `${progress}%`;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function handlePythonMessage(data) {
    try {
        const parsed = JSON.parse(data);

        if (parsed.type === 'file_processing_result') {
            displayFileResult(parsed.message);
        } else if (parsed.type === 'file_processing_error') {
            displayFileError(parsed.message);
        }
    } catch (e) {
        // Not JSON or not a structured message
    }
}

function displayFileResult(message) {
    displayMessage(`✅ ${message}`, 'success', pythonOutput);
}

function displayFileError(message) {
    displayMessage(`❌ ${message}`, 'error', pythonOutput);
}

function displayRawPythonOutput(data) {
    try {
        // Try to parse as JSON first
        const parsed = JSON.parse(data);
        displayMessage(`← Python: ${parsed.message || JSON.stringify(parsed)}`, 'received', rawPythonOutput);
    } catch (e) {
        // If not JSON, display as plain text
        displayMessage(`← Python: ${data.trim()}`, 'received', rawPythonOutput);
    }
}

function displayMessage(message, type, container = rawPythonOutput) {
    const messageElement = document.createElement('div');
    messageElement.className = `python-message ${type}`;
    messageElement.textContent = message;

    container.appendChild(messageElement);
    container.scrollTop = container.scrollHeight;

    // Remove placeholder if it exists
    const placeholder = container.querySelector('.placeholder');
    if (placeholder) {
        placeholder.remove();
    }
}

// Utility function to format timestamps
function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString();
}
