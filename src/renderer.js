// DOM elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const pythonOutput = document.getElementById('pythonOutput');

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    console.log('Renderer process loaded');
    
    // Clear placeholder text
    pythonOutput.innerHTML = '';
    
    // Set up event listeners
    sendButton.addEventListener('click', sendMessageToPython);
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendMessageToPython();
        }
    });
    
    // Listen for Python output
    window.electronAPI.onPythonData((event, data) => {
        displayPythonOutput(data);
    });
    
    // Send initial message to Python to test connection
    setTimeout(() => {
        sendToPython({ type: 'init', message: 'Frontend connected' });
    }, 1000);
});

async function sendMessageToPython() {
    const message = messageInput.value.trim();
    if (!message) return;
    
    try {
        const response = await sendToPython({ 
            type: 'user_message', 
            message: message,
            timestamp: new Date().toISOString()
        });
        
        if (response.success) {
            // Clear input
            messageInput.value = '';
            
            // Display sent message
            displayMessage(`→ Sent: ${message}`, 'sent');
        } else {
            displayMessage(`✗ Error: ${response.error}`, 'error');
        }
    } catch (error) {
        displayMessage(`✗ Error: ${error.message}`, 'error');
    }
}

async function sendToPython(data) {
    try {
        return await window.electronAPI.sendToPython(data);
    } catch (error) {
        console.error('Error sending to Python:', error);
        throw error;
    }
}

function displayPythonOutput(data) {
    try {
        // Try to parse as JSON first
        const parsed = JSON.parse(data);
        displayMessage(`← Python: ${parsed.message || JSON.stringify(parsed)}`, 'received');
    } catch (e) {
        // If not JSON, display as plain text
        displayMessage(`← Python: ${data.trim()}`, 'received');
    }
}

function displayMessage(message, type) {
    const messageElement = document.createElement('div');
    messageElement.className = `python-message ${type}`;
    messageElement.textContent = message;
    
    pythonOutput.appendChild(messageElement);
    pythonOutput.scrollTop = pythonOutput.scrollHeight;
    
    // Remove placeholder if it exists
    const placeholder = pythonOutput.querySelector('.placeholder');
    if (placeholder) {
        placeholder.remove();
    }
}

// Utility function to format timestamps
function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString();
}
