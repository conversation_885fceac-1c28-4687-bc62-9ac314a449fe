<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marker Desktop</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 Marker Desktop</h1>
            <p class="subtitle">Cross-platform desktop app with Python backend</p>
        </header>

        <main>
            <div class="card">
                <h2>Welcome to your Desktop App!</h2>
                <p>This is a placeholder interface for your cross-platform desktop application.</p>
                <p>The app is running with:</p>
                <ul>
                    <li>✅ Electron frontend (JavaScript/HTML/CSS)</li>
                    <li>✅ Python backend process</li>
                    <li>✅ Cross-platform compatibility (Windows, macOS, Linux)</li>
                </ul>
            </div>

            <div class="card">
                <h3>Python Backend Communication</h3>
                <div class="communication-panel">
                    <input type="text" id="messageInput" placeholder="Send message to Python backend..." />
                    <button id="sendButton">Send to Python</button>
                </div>
                <div class="output-panel">
                    <h4>Python Output:</h4>
                    <div id="pythonOutput" class="output-box">
                        <p class="placeholder">Python backend output will appear here...</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>Next Steps</h3>
                <p>To customize this app:</p>
                <ol>
                    <li>Modify <code>python/main.py</code> to add your Python logic</li>
                    <li>Update this HTML/CSS/JS to create your desired interface</li>
                    <li>Use <code>npm run build:win</code> or <code>npm run build:mac</code> to create distributables</li>
                </ol>
            </div>
        </main>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
