<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marker Desktop</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 Marker Desktop</h1>
            <p class="subtitle">Cross-platform desktop app with Python backend</p>
        </header>

        <main>
            <!-- File Drop Zone -->
            <div class="card dropzone-card">
                <div id="dropzone" class="dropzone">
                    <div class="dropzone-content">
                        <div class="dropzone-icon">📁</div>
                        <h2>Drop your file here</h2>
                        <p>or <button id="browseButton" class="browse-button">browse files</button></p>
                        <input type="file" id="fileInput" style="display: none;" />
                        <div class="supported-formats">
                            <small>Supports all file types</small>
                        </div>
                    </div>
                    <div id="fileInfo" class="file-info" style="display: none;">
                        <div class="file-details">
                            <div class="file-icon">📄</div>
                            <div class="file-text">
                                <div class="file-name"></div>
                                <div class="file-size"></div>
                            </div>
                            <button id="removeFile" class="remove-button">✕</button>
                        </div>
                        <div class="file-status">
                            <div id="uploadProgress" class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div id="statusText" class="status-text">Ready to process</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Processing Results -->
            <div id="resultsCard" class="card" style="display: none;">
                <h3>File Processing Results</h3>
                <div class="output-panel">
                    <div id="pythonOutput" class="output-box">
                        <p class="placeholder">File processing results will appear here...</p>
                    </div>
                </div>
            </div>

            <!-- Communication Panel (collapsed by default) -->
            <div class="card">
                <details>
                    <summary>
                        <h3 style="display: inline;">Advanced: Python Backend Communication</h3>
                    </summary>
                    <div class="communication-panel">
                        <input type="text" id="messageInput" placeholder="Send message to Python backend..." />
                        <button id="sendButton">Send to Python</button>
                    </div>
                    <div class="output-panel">
                        <h4>Raw Python Output:</h4>
                        <div id="rawPythonOutput" class="output-box">
                            <p class="placeholder">Raw Python backend output will appear here...</p>
                        </div>
                    </div>
                </details>
            </div>

            <div class="card">
                <h3>Next Steps</h3>
                <p>To customize this app:</p>
                <ol>
                    <li>Modify <code>python/main.py</code> to add your Python logic</li>
                    <li>Update this HTML/CSS/JS to create your desired interface</li>
                    <li>Use <code>npm run build:win</code> or <code>npm run build:mac</code> to create distributables</li>
                </ol>
            </div>
        </main>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
