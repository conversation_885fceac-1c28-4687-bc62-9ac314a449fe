const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

let mainWindow;
let pythonProcess;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../assets/icon.png')
  });

  // Load the app
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // Start Python backend
  startPythonBackend();
}

function startPythonBackend() {
  const pythonScriptPath = path.join(__dirname, '../python/main.py');
  
  // Try to use bundled Python first, then system Python
  let pythonCommand = 'python';
  
  if (app.isPackaged) {
    // In production, use bundled Python
    const pythonPath = path.join(process.resourcesPath, 'python');
    pythonCommand = path.join(pythonPath, 'python');
  }

  pythonProcess = spawn(pythonCommand, [pythonScriptPath]);

  pythonProcess.stdout.on('data', (data) => {
    console.log(`Python stdout: ${data}`);
    // Send data to renderer process if needed
    if (mainWindow) {
      mainWindow.webContents.send('python-data', data.toString());
    }
  });

  pythonProcess.stderr.on('data', (data) => {
    console.error(`Python stderr: ${data}`);
  });

  pythonProcess.on('close', (code) => {
    console.log(`Python process exited with code ${code}`);
  });
}

// Handle IPC messages from renderer
ipcMain.handle('send-to-python', async (event, message) => {
  return new Promise((resolve) => {
    if (pythonProcess) {
      pythonProcess.stdin.write(JSON.stringify(message) + '\n');
      resolve({ success: true });
    } else {
      resolve({ success: false, error: 'Python process not running' });
    }
  });
});

// App event handlers
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  // Kill Python process when app closes
  if (pythonProcess) {
    pythonProcess.kill();
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.on('before-quit', () => {
  // Ensure Python process is killed
  if (pythonProcess) {
    pythonProcess.kill();
  }
});
