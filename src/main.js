const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const os = require('os');
const { spawn } = require('child_process');

let mainWindow;
let pythonProcess;
let tempDir;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../assets/icon.png')
  });

  // Load the app
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // Start Python backend
  startPythonBackend();
}

function startPythonBackend() {
  const pythonScriptPath = path.join(__dirname, '../python/main.py');
  
  // Try to use bundled Python first, then system Python
  let pythonCommand = 'python';
  
  if (app.isPackaged) {
    // In production, use bundled Python
    const pythonPath = path.join(process.resourcesPath, 'python');
    pythonCommand = path.join(pythonPath, 'python');
  }

  pythonProcess = spawn(pythonCommand, [pythonScriptPath]);

  pythonProcess.stdout.on('data', (data) => {
    console.log(`Python stdout: ${data}`);
    // Send data to renderer process if needed
    if (mainWindow) {
      mainWindow.webContents.send('python-data', data.toString());
    }
  });

  pythonProcess.stderr.on('data', (data) => {
    console.error(`Python stderr: ${data}`);
  });

  pythonProcess.on('close', (code) => {
    console.log(`Python process exited with code ${code}`);
  });
}

// Create temp directory for file storage
async function createTempDir() {
  try {
    const appTempDir = path.join(os.tmpdir(), 'marker-desktop');
    await fs.mkdir(appTempDir, { recursive: true });
    tempDir = appTempDir;
    console.log('Temp directory created:', tempDir);
    return tempDir;
  } catch (error) {
    console.error('Error creating temp directory:', error);
    throw error;
  }
}

// Clean up temp directory
async function cleanupTempDir() {
  if (tempDir) {
    try {
      await fs.rmdir(tempDir, { recursive: true });
      console.log('Temp directory cleaned up');
    } catch (error) {
      console.error('Error cleaning up temp directory:', error);
    }
  }
}

// Handle IPC messages from renderer
ipcMain.handle('send-to-python', async (event, message) => {
  return new Promise((resolve) => {
    if (pythonProcess) {
      // Add temp directory info to message if it's a file-related message
      if (message.type === 'file_uploaded' && tempDir) {
        message.tempDir = tempDir;
      }

      pythonProcess.stdin.write(JSON.stringify(message) + '\n');
      resolve({ success: true });
    } else {
      resolve({ success: false, error: 'Python process not running' });
    }
  });
});

// Handle file saving
ipcMain.handle('save-file', async (event, fileData) => {
  try {
    if (!tempDir) {
      await createTempDir();
    }

    // Generate unique filename
    const timestamp = Date.now();
    const sanitizedName = fileData.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const tempFileName = `${timestamp}_${sanitizedName}`;
    const tempFilePath = path.join(tempDir, tempFileName);

    // Write file to temp directory
    const buffer = Buffer.from(fileData.buffer);
    await fs.writeFile(tempFilePath, buffer);

    console.log('File saved to:', tempFilePath);

    return {
      success: true,
      tempPath: tempFilePath,
      fileName: tempFileName
    };
  } catch (error) {
    console.error('Error saving file:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// App event handlers
app.whenReady().then(async () => {
  await createTempDir();
  createWindow();
});

app.on('window-all-closed', () => {
  // Kill Python process when app closes
  if (pythonProcess) {
    pythonProcess.kill();
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.on('before-quit', async () => {
  // Ensure Python process is killed
  if (pythonProcess) {
    pythonProcess.kill();
  }

  // Clean up temp directory
  await cleanupTempDir();
});
