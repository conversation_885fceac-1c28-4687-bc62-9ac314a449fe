const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const os = require('os');
const { spawn } = require('child_process');

let mainWindow;
let pythonProcess;
let tempDir;
let isShuttingDown = false;

async function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../assets/icon.png')
  });

  // Load the app
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // Start Python backend
  await startPythonBackend();
}

async function findPythonCommand() {
  const { spawn } = require('child_process');

  // List of possible Python commands to try
  const pythonCommands = ['python3', 'python', 'py'];

  for (const cmd of pythonCommands) {
    try {
      await new Promise((resolve, reject) => {
        const testProcess = spawn(cmd, ['--version'], { stdio: 'pipe' });

        testProcess.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error(`Command ${cmd} failed with code ${code}`));
          }
        });

        testProcess.on('error', (error) => {
          reject(error);
        });

        // Timeout after 3 seconds
        setTimeout(() => {
          testProcess.kill();
          reject(new Error(`Command ${cmd} timed out`));
        }, 3000);
      });

      console.log(`Found Python command: ${cmd}`);
      return cmd;
    } catch (error) {
      console.log(`Python command ${cmd} not available: ${error.message}`);
    }
  }

  throw new Error('No Python installation found. Please install Python 3.x and ensure it\'s in your PATH.');
}

async function startPythonBackend() {
  const pythonScriptPath = path.join(__dirname, '../python/main.py');

  try {
    let pythonCommand;

    if (app.isPackaged) {
      // In production, use bundled Python
      const pythonPath = path.join(process.resourcesPath, 'python');
      pythonCommand = path.join(pythonPath, 'python');
    } else {
      // In development, find available Python command
      pythonCommand = await findPythonCommand();
    }

    console.log(`Starting Python backend with command: ${pythonCommand}`);
    console.log(`Python script path: ${pythonScriptPath}`);

    pythonProcess = spawn(pythonCommand, [pythonScriptPath], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    pythonProcess.stdout.on('data', (data) => {
      console.log(`Python stdout: ${data}`);
      // Send data to renderer process if needed
      if (mainWindow) {
        mainWindow.webContents.send('python-data', data.toString());
      }
    });

    pythonProcess.stderr.on('data', (data) => {
      console.error(`Python stderr: ${data}`);
      // Also send errors to renderer for debugging
      if (mainWindow) {
        mainWindow.webContents.send('python-data', `ERROR: ${data.toString()}`);
      }
    });

    pythonProcess.on('close', (code) => {
      console.log(`Python process exited with code ${code}`);
      if (code !== 0 && mainWindow) {
        mainWindow.webContents.send('python-data', `Python process exited with code ${code}`);
      }
    });

    pythonProcess.on('error', (error) => {
      console.error(`Failed to start Python process: ${error.message}`);
      if (mainWindow) {
        mainWindow.webContents.send('python-data', `Failed to start Python: ${error.message}`);
      }
    });

  } catch (error) {
    console.error(`Error starting Python backend: ${error.message}`);
    if (mainWindow) {
      mainWindow.webContents.send('python-data', `Error: ${error.message}`);
    }
  }
}

// Create temp directory for file storage
async function createTempDir() {
  try {
    const appTempDir = path.join(os.tmpdir(), 'marker-desktop');
    await fs.mkdir(appTempDir, { recursive: true });
    tempDir = appTempDir;
    console.log('Temp directory created:', tempDir);
    return tempDir;
  } catch (error) {
    console.error('Error creating temp directory:', error);
    throw error;
  }
}

// Clean up temp directory
async function cleanupTempDir() {
  if (tempDir) {
    try {
      await fs.rmdir(tempDir, { recursive: true });
      console.log('Temp directory cleaned up');
    } catch (error) {
      console.error('Error cleaning up temp directory:', error);
    }
  }
}

// Handle IPC messages from renderer
ipcMain.handle('send-to-python', async (event, message) => {
  return new Promise((resolve) => {
    if (pythonProcess && !isShuttingDown) {
      try {
        // Add temp directory info to message if it's a file-related message
        if (message.type === 'file_uploaded' && tempDir) {
          message.tempDir = tempDir;
        }

        pythonProcess.stdin.write(JSON.stringify(message) + '\n');
        resolve({ success: true });
      } catch (error) {
        console.error('Error writing to Python process:', error);
        resolve({ success: false, error: 'Failed to send message to Python' });
      }
    } else {
      resolve({ success: false, error: 'Python process not running or shutting down' });
    }
  });
});

// Handle file saving
ipcMain.handle('save-file', async (event, fileData) => {
  try {
    if (!tempDir) {
      await createTempDir();
    }

    // Generate unique filename
    const timestamp = Date.now();
    const sanitizedName = fileData.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const tempFileName = `${timestamp}_${sanitizedName}`;
    const tempFilePath = path.join(tempDir, tempFileName);

    // Write file to temp directory
    const buffer = Buffer.from(fileData.buffer);
    await fs.writeFile(tempFilePath, buffer);

    console.log('File saved to:', tempFilePath);

    return {
      success: true,
      tempPath: tempFilePath,
      fileName: tempFileName
    };
  } catch (error) {
    console.error('Error saving file:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// App event handlers
app.whenReady().then(async () => {
  await createTempDir();
  await createWindow();
});

app.on('window-all-closed', async () => {
  isShuttingDown = true;

  // Gracefully close Python process
  if (pythonProcess) {
    try {
      // Try to close stdin first
      if (pythonProcess.stdin && !pythonProcess.stdin.destroyed) {
        pythonProcess.stdin.end();
      }

      // Wait a bit for graceful shutdown
      await new Promise(resolve => setTimeout(resolve, 500));

      // Force kill if still running
      if (!pythonProcess.killed) {
        pythonProcess.kill('SIGTERM');

        // Wait a bit more, then force kill
        await new Promise(resolve => setTimeout(resolve, 1000));
        if (!pythonProcess.killed) {
          pythonProcess.kill('SIGKILL');
        }
      }
    } catch (error) {
      console.error('Error closing Python process:', error);
    }
  }

  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.on('before-quit', async () => {
  isShuttingDown = true;

  // Gracefully close Python process
  if (pythonProcess && !pythonProcess.killed) {
    try {
      // Try to close stdin first
      if (pythonProcess.stdin && !pythonProcess.stdin.destroyed) {
        pythonProcess.stdin.end();
      }

      // Wait for graceful shutdown
      await new Promise(resolve => setTimeout(resolve, 500));

      // Force kill if still running
      if (!pythonProcess.killed) {
        pythonProcess.kill('SIGTERM');
      }
    } catch (error) {
      console.error('Error in before-quit Python cleanup:', error);
    }
  }

  // Clean up temp directory
  try {
    await cleanupTempDir();
  } catch (error) {
    console.error('Error cleaning up temp directory:', error);
  }
});
