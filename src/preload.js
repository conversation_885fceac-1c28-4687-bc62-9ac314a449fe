const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  sendToPython: (message) => ipcRenderer.invoke('send-to-python', message),
  onPythonData: (callback) => ipcRenderer.on('python-data', callback),
  removePythonDataListener: (callback) => ipcRenderer.removeListener('python-data', callback)
});
