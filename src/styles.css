* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.card h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.card h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.4rem;
}

.card h4 {
    color: #4a5568;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.card p {
    line-height: 1.6;
    margin-bottom: 15px;
    color: #666;
}

.card ul, .card ol {
    margin-left: 20px;
    margin-bottom: 15px;
}

.card li {
    margin-bottom: 8px;
    color: #666;
}

.communication-panel {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

#messageInput {
    flex: 1;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

#messageInput:focus {
    outline: none;
    border-color: #667eea;
}

#sendButton {
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: transform 0.2s ease;
}

#sendButton:hover {
    transform: translateY(-2px);
}

#sendButton:active {
    transform: translateY(0);
}

.output-panel {
    margin-top: 20px;
}

.output-box {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    min-height: 120px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.placeholder {
    color: #a0aec0;
    font-style: italic;
}

.python-message {
    margin-bottom: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    border-left: 4px solid #38b2ac;
    background: #e6fffa;
}

.python-message.success {
    background: #f0fff4;
    border-left-color: #38a169;
    color: #2f855a;
}

.python-message.error {
    background: #fed7d7;
    border-left-color: #e53e3e;
    color: #c53030;
}

.python-message.received {
    background: #e6fffa;
    border-left-color: #38b2ac;
    color: #2c7a7b;
}

.python-message.sent {
    background: #ebf8ff;
    border-left-color: #3182ce;
    color: #2b6cb0;
}

code {
    background: #f1f5f9;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #e53e3e;
}

/* Dropzone Styles */
.dropzone-card {
    margin-bottom: 30px;
}

.dropzone {
    border: 3px dashed #cbd5e0;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    background: #f8fafc;
    position: relative;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dropzone.dragover {
    border-color: #667eea;
    background: #edf2f7;
    transform: scale(1.02);
}

.dropzone-content {
    pointer-events: none;
}

.dropzone-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.7;
}

.dropzone h2 {
    color: #4a5568;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.dropzone p {
    color: #718096;
    margin-bottom: 15px;
}

.browse-button {
    background: none;
    border: none;
    color: #667eea;
    text-decoration: underline;
    cursor: pointer;
    font-size: inherit;
    pointer-events: all;
    padding: 0;
}

.browse-button:hover {
    color: #5a67d8;
}

.supported-formats {
    margin-top: 15px;
}

.supported-formats small {
    color: #a0aec0;
    font-size: 0.8rem;
}

/* File Info Styles */
.file-info {
    width: 100%;
}

.file-details {
    display: flex;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.file-icon {
    font-size: 2rem;
    margin-right: 15px;
}

.file-text {
    flex: 1;
    text-align: left;
}

.file-name {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 5px;
}

.file-size {
    color: #718096;
    font-size: 0.9rem;
}

.remove-button {
    background: #fed7d7;
    border: none;
    color: #e53e3e;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.remove-button:hover {
    background: #feb2b2;
}

.file-status {
    text-align: left;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.status-text {
    color: #4a5568;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Collapsible section */
details {
    margin-top: 10px;
}

summary {
    cursor: pointer;
    padding: 10px 0;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 15px;
}

summary:hover {
    color: #667eea;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .card {
        padding: 20px;
    }
    
    .communication-panel {
        flex-direction: column;
    }
}
