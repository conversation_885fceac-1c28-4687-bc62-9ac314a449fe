* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.card h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.card h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.4rem;
}

.card h4 {
    color: #4a5568;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.card p {
    line-height: 1.6;
    margin-bottom: 15px;
    color: #666;
}

.card ul, .card ol {
    margin-left: 20px;
    margin-bottom: 15px;
}

.card li {
    margin-bottom: 8px;
    color: #666;
}

.communication-panel {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

#messageInput {
    flex: 1;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

#messageInput:focus {
    outline: none;
    border-color: #667eea;
}

#sendButton {
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: transform 0.2s ease;
}

#sendButton:hover {
    transform: translateY(-2px);
}

#sendButton:active {
    transform: translateY(0);
}

.output-panel {
    margin-top: 20px;
}

.output-box {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    min-height: 120px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.placeholder {
    color: #a0aec0;
    font-style: italic;
}

.python-message {
    margin-bottom: 8px;
    padding: 5px;
    background: #e6fffa;
    border-left: 3px solid #38b2ac;
    border-radius: 4px;
}

code {
    background: #f1f5f9;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #e53e3e;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .card {
        padding: 20px;
    }
    
    .communication-panel {
        flex-direction: column;
    }
}
