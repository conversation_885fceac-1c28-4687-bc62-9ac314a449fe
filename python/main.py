#!/usr/bin/env python3
"""
Main Python backend for the Marker Desktop application.
This script runs in the background and communicates with the Electron frontend.
"""

import sys
import json
import time
import threading
import os
from datetime import datetime
from pathlib import Path

class MarkerBackend:
    def __init__(self):
        self.running = True
        self.message_count = 0
        
    def log(self, message):
        """Log messages with timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}", flush=True)
        
    def send_message(self, message_type, content):
        """Send a message to the frontend"""
        message = {
            "type": message_type,
            "message": content,
            "timestamp": datetime.now().isoformat(),
            "count": self.message_count
        }
        print(json.dumps(message), flush=True)
        self.message_count += 1
        
    def process_file(self, file_info):
        """Process uploaded file"""
        try:
            file_path = file_info.get('tempPath')
            file_name = file_info.get('name')
            file_size = file_info.get('size')
            file_type = file_info.get('type', 'unknown')

            if not file_path or not os.path.exists(file_path):
                self.send_message('file_processing_error', f'File not found: {file_path}')
                return

            self.log(f"Processing file: {file_name} ({file_size} bytes)")
            self.send_message('file_processing_result', f'Started processing file: {file_name}')

            # Get file info
            file_stats = os.stat(file_path)
            file_extension = Path(file_name).suffix.lower()

            # Basic file analysis
            analysis_results = []
            analysis_results.append(f"📄 File: {file_name}")
            analysis_results.append(f"📏 Size: {self.format_file_size(file_size)}")
            analysis_results.append(f"🏷️ Type: {file_type}")
            analysis_results.append(f"📁 Extension: {file_extension}")
            analysis_results.append(f"📅 Modified: {datetime.fromtimestamp(file_stats.st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")

            # Simulate processing based on file type
            if file_extension in ['.txt', '.md', '.py', '.js', '.html', '.css', '.json']:
                # Text file processing
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        words = len(content.split())
                        chars = len(content)

                    analysis_results.append(f"📝 Lines: {len(lines)}")
                    analysis_results.append(f"🔤 Words: {words}")
                    analysis_results.append(f"🔢 Characters: {chars}")

                    # Show first few lines as preview
                    if lines:
                        preview_lines = lines[:3]
                        analysis_results.append("👀 Preview:")
                        for i, line in enumerate(preview_lines, 1):
                            if line.strip():
                                analysis_results.append(f"  {i}: {line[:50]}{'...' if len(line) > 50 else ''}")

                except UnicodeDecodeError:
                    analysis_results.append("⚠️ Could not read as text file")

            elif file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                analysis_results.append("🖼️ Image file detected")
                analysis_results.append("ℹ️ Image analysis would require additional libraries (PIL/Pillow)")

            elif file_extension in ['.pdf']:
                analysis_results.append("📋 PDF file detected")
                analysis_results.append("ℹ️ PDF analysis would require additional libraries (PyPDF2/pdfplumber)")

            elif file_extension in ['.csv']:
                analysis_results.append("📊 CSV file detected")
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                        if first_line:
                            columns = first_line.split(',')
                            analysis_results.append(f"📋 Columns detected: {len(columns)}")
                            analysis_results.append(f"🏷️ Headers: {', '.join(columns[:5])}{'...' if len(columns) > 5 else ''}")
                except Exception as e:
                    analysis_results.append(f"⚠️ Error reading CSV: {str(e)}")

            else:
                analysis_results.append("📦 Binary or unknown file type")
                analysis_results.append("ℹ️ Basic file information only")

            # Send results
            for result in analysis_results:
                self.send_message('file_processing_result', result)
                time.sleep(0.1)  # Small delay for better UX

            self.send_message('file_processing_result', '✅ File analysis complete!')

        except Exception as e:
            self.log(f"Error processing file: {e}")
            self.send_message('file_processing_error', f'Error processing file: {str(e)}')

    def format_file_size(self, size_bytes):
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

    def process_message(self, data):
        """Process incoming messages from the frontend"""
        try:
            message_type = data.get('type', 'unknown')
            content = data.get('message', '')

            if message_type == 'init':
                self.send_message('response', 'Python backend initialized successfully! 🐍')

            elif message_type == 'file_uploaded':
                file_info = data.get('file', {})
                self.process_file(file_info)

            elif message_type == 'user_message':
                # Echo the message back with some processing
                response = f"Received: '{content}' - Processed at {datetime.now().strftime('%H:%M:%S')}"
                self.send_message('response', response)

                # Simulate some background processing
                if 'hello' in content.lower():
                    time.sleep(1)  # Simulate processing delay
                    self.send_message('response', 'Hello there! 👋 Python backend is working!')

                elif 'time' in content.lower():
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self.send_message('response', f'Current time: {current_time}')

                elif 'test' in content.lower():
                    self.send_message('response', 'Test successful! All systems operational. ✅')

            else:
                self.send_message('response', f'Unknown message type: {message_type}')

        except Exception as e:
            self.send_message('error', f'Error processing message: {str(e)}')
            
    def heartbeat_worker(self):
        """Send periodic heartbeat messages"""
        while self.running:
            time.sleep(30)  # Send heartbeat every 30 seconds
            if self.running:
                self.send_message('heartbeat', 'Python backend is alive')
                
    def run(self):
        """Main run loop"""
        self.log("Python backend starting...")
        self.send_message('status', 'Python backend started successfully')
        
        # Start heartbeat thread
        heartbeat_thread = threading.Thread(target=self.heartbeat_worker, daemon=True)
        heartbeat_thread.start()
        
        # Main message processing loop
        try:
            for line in sys.stdin:
                if not self.running:
                    break
                    
                line = line.strip()
                if not line:
                    continue
                    
                try:
                    data = json.loads(line)
                    self.process_message(data)
                except json.JSONDecodeError as e:
                    self.log(f"JSON decode error: {e}")
                    self.send_message('error', f'Invalid JSON received: {line}')
                    
        except KeyboardInterrupt:
            self.log("Received interrupt signal")
        except Exception as e:
            self.log(f"Unexpected error: {e}")
        finally:
            self.running = False
            self.log("Python backend shutting down...")

if __name__ == "__main__":
    backend = MarkerBackend()
    backend.run()
