#!/usr/bin/env python3
"""
Main Python backend for the Marker Desktop application.
This script runs in the background and communicates with the Electron frontend.
"""

import sys
import json
import time
import threading
from datetime import datetime

class MarkerBackend:
    def __init__(self):
        self.running = True
        self.message_count = 0
        
    def log(self, message):
        """Log messages with timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}", flush=True)
        
    def send_message(self, message_type, content):
        """Send a message to the frontend"""
        message = {
            "type": message_type,
            "message": content,
            "timestamp": datetime.now().isoformat(),
            "count": self.message_count
        }
        print(json.dumps(message), flush=True)
        self.message_count += 1
        
    def process_message(self, data):
        """Process incoming messages from the frontend"""
        try:
            message_type = data.get('type', 'unknown')
            content = data.get('message', '')
            
            if message_type == 'init':
                self.send_message('response', 'Python backend initialized successfully! 🐍')
                
            elif message_type == 'user_message':
                # Echo the message back with some processing
                response = f"Received: '{content}' - Processed at {datetime.now().strftime('%H:%M:%S')}"
                self.send_message('response', response)
                
                # Simulate some background processing
                if 'hello' in content.lower():
                    time.sleep(1)  # Simulate processing delay
                    self.send_message('response', 'Hello there! 👋 Python backend is working!')
                    
                elif 'time' in content.lower():
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self.send_message('response', f'Current time: {current_time}')
                    
                elif 'test' in content.lower():
                    self.send_message('response', 'Test successful! All systems operational. ✅')
                    
            else:
                self.send_message('response', f'Unknown message type: {message_type}')
                
        except Exception as e:
            self.send_message('error', f'Error processing message: {str(e)}')
            
    def heartbeat_worker(self):
        """Send periodic heartbeat messages"""
        while self.running:
            time.sleep(30)  # Send heartbeat every 30 seconds
            if self.running:
                self.send_message('heartbeat', 'Python backend is alive')
                
    def run(self):
        """Main run loop"""
        self.log("Python backend starting...")
        self.send_message('status', 'Python backend started successfully')
        
        # Start heartbeat thread
        heartbeat_thread = threading.Thread(target=self.heartbeat_worker, daemon=True)
        heartbeat_thread.start()
        
        # Main message processing loop
        try:
            for line in sys.stdin:
                if not self.running:
                    break
                    
                line = line.strip()
                if not line:
                    continue
                    
                try:
                    data = json.loads(line)
                    self.process_message(data)
                except json.JSONDecodeError as e:
                    self.log(f"JSON decode error: {e}")
                    self.send_message('error', f'Invalid JSON received: {line}')
                    
        except KeyboardInterrupt:
            self.log("Received interrupt signal")
        except Exception as e:
            self.log(f"Unexpected error: {e}")
        finally:
            self.running = False
            self.log("Python backend shutting down...")

if __name__ == "__main__":
    backend = MarkerBackend()
    backend.run()
