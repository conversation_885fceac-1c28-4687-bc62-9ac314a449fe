#!/usr/bin/env python3
"""
Main Python backend for the Marker Desktop application.
This script runs in the background and communicates with the Electron frontend.
"""

import sys
import json
import time
import threading
import os
from datetime import datetime
from pathlib import Path

class MarkerBackend:
    def __init__(self):
        self.running = True
        self.message_count = 0
        
    def log(self, message):
        """Log messages with timestamp to stderr"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}", file=sys.stderr, flush=True)
        
    def send_message(self, message_type, content):
        """Send a message to the frontend"""
        message = {
            "type": message_type,
            "message": content,
            "timestamp": datetime.now().isoformat(),
            "count": self.message_count
        }
        print(json.dumps(message), flush=True)
        self.message_count += 1
        
    def process_file(self, file_info):
        """Process uploaded file and calculate its size"""
        try:
            file_path = file_info.get('tempPath')
            file_name = file_info.get('name')

            if not file_path or not os.path.exists(file_path):
                self.send_message('file_processing_error', f'File not found: {file_path}')
                return

            self.log(f"Processing file: {file_name}")

            # Calculate actual file size from the saved file
            actual_file_size = os.path.getsize(file_path)
            formatted_size = self.format_file_size(actual_file_size)

            self.log(f"File size calculated: {actual_file_size} bytes ({formatted_size})")

            # Send file information back to frontend
            file_result = {
                "type": "file_info",
                "data": {
                    "name": file_name,
                    "size_bytes": actual_file_size,
                    "size_formatted": formatted_size,
                    "path": file_path
                },
                "timestamp": datetime.now().isoformat()
            }

            # Send as JSON message
            print(json.dumps(file_result), flush=True)

        except Exception as e:
            self.log(f"Error processing file: {e}")
            error_result = {
                "type": "file_error",
                "data": {
                    "error": str(e)
                },
                "timestamp": datetime.now().isoformat()
            }
            print(json.dumps(error_result), flush=True)

    def format_file_size(self, size_bytes):
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

    def process_message(self, data):
        """Process incoming messages from the frontend"""
        try:
            message_type = data.get('type', 'unknown')
            content = data.get('message', '')

            if message_type == 'init':
                self.send_message('response', 'Python backend initialized successfully! 🐍')

            elif message_type == 'file_uploaded':
                file_info = data.get('file', {})
                self.process_file(file_info)

            elif message_type == 'user_message':
                # Echo the message back with some processing
                response = f"Received: '{content}' - Processed at {datetime.now().strftime('%H:%M:%S')}"
                self.send_message('response', response)

                # Simulate some background processing
                if 'hello' in content.lower():
                    time.sleep(1)  # Simulate processing delay
                    self.send_message('response', 'Hello there! 👋 Python backend is working!')

                elif 'time' in content.lower():
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self.send_message('response', f'Current time: {current_time}')

                elif 'test' in content.lower():
                    self.send_message('response', 'Test successful! All systems operational. ✅')

            else:
                self.send_message('response', f'Unknown message type: {message_type}')

        except Exception as e:
            self.send_message('error', f'Error processing message: {str(e)}')
            
    def heartbeat_worker(self):
        """Send periodic heartbeat messages"""
        while self.running:
            time.sleep(30)  # Send heartbeat every 30 seconds
            if self.running:
                self.send_message('heartbeat', 'Python backend is alive')
                
    def run(self):
        """Main run loop"""
        self.log("Python backend starting...")
        self.send_message('status', 'Python backend started successfully')

        # Start heartbeat thread
        heartbeat_thread = threading.Thread(target=self.heartbeat_worker, daemon=True)
        heartbeat_thread.start()

        # Main message processing loop
        try:
            for line in sys.stdin:
                if not self.running:
                    break

                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                    self.process_message(data)
                except json.JSONDecodeError as e:
                    self.log(f"JSON decode error: {e}")
                    self.send_message('error', f'Invalid JSON received: {line}')
                except Exception as e:
                    self.log(f"Error processing message: {e}")

        except KeyboardInterrupt:
            self.log("Received interrupt signal")
        except EOFError:
            self.log("Stdin closed, shutting down gracefully")
        except Exception as e:
            self.log(f"Unexpected error: {e}")
        finally:
            self.running = False
            self.log("Python backend shutting down...")
            # Flush any remaining output
            sys.stdout.flush()
            sys.stderr.flush()

if __name__ == "__main__":
    backend = MarkerBackend()
    backend.run()
