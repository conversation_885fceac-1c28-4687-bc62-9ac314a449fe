directories:
  output: dist
  buildResources: build
appId: com.yourcompany.marker-desktop
productName: Marker Desktop
files:
  - filter:
      - src/**/*
      - python/**/*
      - node_modules/**/*
      - '!src/react/**/*'
extraResources:
  - from: python
    to: python
win:
  target: nsis
  icon: assets/icon.ico
mac:
  target: dmg
  icon: assets/icon.icns
linux:
  target: AppImage
  icon: assets/icon.png
electronVersion: 27.3.11
