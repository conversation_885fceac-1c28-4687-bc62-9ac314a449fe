{"name": "marker-desktop", "version": "1.0.0", "description": "A cross-platform desktop app with Python backend", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "desktop", "cross-platform", "python"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"child_process": "^1.0.2"}, "build": {"appId": "com.yourcompany.marker-desktop", "productName": "<PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["src/**/*", "python/**/*", "node_modules/**/*"], "extraResources": [{"from": "python", "to": "python"}], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}